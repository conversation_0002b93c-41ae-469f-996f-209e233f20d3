<?php
/**
 * Session Manager yang Di<PERSON>amanannya
 */

function safe_session_start() {
    if (session_status() === PHP_SESSION_NONE) {
        // Opsi untuk meningkatkan keamanan cookie sesi
        $cookieParams = [
            'lifetime' => 0, // <PERSON><PERSON> be<PERSON> sampai browser ditutup
            'path' => '/',
            'domain' => '', // Ganti dengan domain Anda jika perlu
            'secure' => isset($_SERVER['HTTPS']), // True jika menggunakan HTTPS
            'httponly' => true, // Mencegah akses dari JavaScript (XSS)
            'samesite' => 'Lax' // Perlindungan dasar terhadap CSRF
        ];
        session_set_cookie_params($cookieParams);
        session_start();
    }
}

function check_session_auth($allowed_levels) {
    safe_session_start();

    // Cek apakah user sudah login (petugas atau client)
    if (!isset($_SESSION['id_petugas']) && !isset($_SESSION['id_client'])) {
        header("Location: ../index.php");
        exit();
    }

    // Ubah parameter menjadi array jika bukan array (untuk jaga-jaga)
    if (!is_array($allowed_levels)) {
        $allowed_levels = [$allowed_levels];
    }

    // Cek apakah level user yang login ada di dalam daftar yang diizinkan
    if (!isset($_SESSION['level']) || !in_array($_SESSION['level'], $allowed_levels)) {
        // Jika tidak ada dalam daftar, redirect ke halaman "Akses Ditolak"
        header("Location: ../unauthorized.php"); 
        exit();
    }
}

function destroy_session() {
    safe_session_start();

    // 1. Hapus semua variabel sesi
    $_SESSION = array();

    // 2. Hapus cookie sesi dari browser
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // 3. Hancurkan sesi di server
    session_destroy();
}

/**
 * Panggil fungsi ini di halaman logout Anda, lalu redirect.
 * Contoh di file logout.php:
 *
 * require 'includes/session_manager.php';
 * destroy_session_and_redirect();
 *
 */
function destroy_session_and_redirect() {
    destroy_session();
    header("Location: ../index.php");
    exit();
}