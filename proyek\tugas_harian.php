<?php
require_once '../includes/session_manager.php';
// Admin dan Proyek bisa melihat riwayat tugas yang disetujui
check_session_auth(['admin', 'proyek']);
require_once '../koneksi.php';

$page_title = "Riwayat Tugas Disetujui";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">Riwayat Proyek</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 bg-transparent p-0">
                        <li class="breadcrumb-item"><a href="admin.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Riwayat Proyek</li>
                    </ol>
                </nav>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-history mr-2"></i>History Proyek yang Telah Disetujui</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="text-center">No</th>
                                    <th>Nama Proyek</th>
                                    <th>Nama Tugas</th>
                                    <th>Client</th>
                                    <th class="text-center">Timeline</th>
                                    <th class="text-center">Status Akhir</th>
                                    </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sql = mysqli_query($koneksi, "
                                    SELECT 
                                        tp.*, p.nama_proyek, u.first_name, u.last_name
                                    FROM tugas_proyek tp
                                    JOIN proyek p ON tp.proyek_id = p.id
                                    JOIN users u ON p.client_id = u.id
                                    WHERE tp.status_verifikasi = 'approved' 
                                    ORDER BY tp.tgl_mulai DESC
                                ");

                                if (mysqli_num_rows($sql) > 0) {
                                    $no = 1;
                                    while ($data = mysqli_fetch_array($sql)) {
                                ?>
                                <tr>
                                    <td class="text-center align-middle"><?php echo $no++; ?></td>
                                    <td class="align-middle"><?php echo htmlspecialchars($data['nama_proyek']); ?></td>
                                    <td class="align-middle">
                                        <strong><?php echo htmlspecialchars($data['nama_kegiatan']); ?></strong>
                                        <p class="text-muted small mb-0"><?php echo htmlspecialchars($data['deskripsi']); ?></p>
                                    </td>
                                    <td class="align-middle"><?php echo htmlspecialchars($data['first_name'] . ' ' . $data['last_name']); ?></td>
                                    <td class="text-center align-middle">
                                        <small>
                                            <?php echo date('d M Y', strtotime($data['tgl_mulai'])); ?> - 
                                            <?php echo date('d M Y', strtotime($data['tgl_selesai'])); ?>
                                        </small>
                                    </td>
                                    <td class="text-center align-middle">
                                        <?php
                                        $status = $data['status'];
                                        $badgeClass = 'secondary';
                                        if ($status == 'proses') { $badgeClass = 'warning'; } 
                                        elseif ($status == 'selesai') { $badgeClass = 'success'; } 
                                        elseif ($status == 'batal') { $badgeClass = 'danger'; }
                                        ?>
                                        <span class="badge badge-<?php echo $badgeClass; ?>"><?php echo ucfirst($status); ?></span>
                                    </td>
                                    </tr>
                                <?php } } else { ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">Belum Ada Tugas yang Disetujui</td>
                                </tr>
                                <?php } ?>
                                </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>
</body>
</html>