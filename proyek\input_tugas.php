<?php
require_once '../includes/session_manager.php';
check_session_auth('proyek');
require_once '../koneksi.php';

$page_title = "Input Tugas Baru";
include 'includes/header/header.php';

// Ambil daftar proyek dari database untuk ditampilkan di dropdown
$projects = [];
$sql_projects = "SELECT p.id, p.nama_proyek, u.first_name, u.last_name 
                 FROM proyek p 
                 JOIN users u ON p.client_id = u.id 
                 ORDER BY p.nama_proyek";
$result_projects = mysqli_query($koneksi, $sql_projects);
if ($result_projects) {
    while ($row = mysqli_fetch_assoc($result_projects)) {
        $projects[] = $row;
    }
}
?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">
            
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">Input Tugas Baru</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 bg-transparent p-0">
                        <li class="breadcrumb-item"><a href="proyek.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="kelola_proyek.php">Kelola Proyek</a></li>
                        <li class="breadcrumb-item active">Input Tugas</li>
                    </ol>
                </nav>
            </div>

            <div class="row">
                <div class="col-lg-8 col-md-10 mx-auto">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-plus-circle mr-2"></i>Form Input Tugas</h6>
                        </div>
                        <div class="card-body">
                            <form action="../proyek/simpan_input.php" method="post">

                                <div class="form-group">
                                    <label for="proyek_id" class="font-weight-bold">Pilih Proyek</label>
                                    <select class="form-control" id="proyek_id" name="proyek_id" required>
                                        <option value="">-- Pilih proyek untuk tugas ini --</option>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?php echo $project['id']; ?>">
                                                <?php echo htmlspecialchars($project['nama_proyek'] . ' (' . $project['first_name'] . ' ' . $project['last_name'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-text text-muted">Setiap tugas harus ditujukan untuk sebuah proyek.</small>
                                </div>

                                <div class="form-group">
                                    <label for="nama_kegiatan" class="font-weight-bold">Nama Tugas (Kegiatan)</label>
                                    <input type="text" class="form-control" id="nama_kegiatan" name="nama_kegiatan" required>
                                </div>
                                <div class="form-group">
                                    <label for="deskripsi" class="font-weight-bold">Deskripsi Tugas</label>
                                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="tgl_mulai" class="font-weight-bold">Tanggal Mulai</label>
                                            <input type="date" class="form-control" id="tgl_mulai" name="tgl_mulai" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="tgl_selesai" class="font-weight-bold">Tanggal Selesai (Estimasi)</label>
                                            <input type="date" class="form-control" id="tgl_selesai" name="tgl_selesai" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6 mb-2">
                                        <button type="submit" class="btn btn-primary btn-block"><i class="fas fa-save mr-2"></i>Simpan Tugas</button>
                                    </div>
                                    <div class="col-sm-6 mb-2">
                                        <button type="reset" class="btn btn-warning btn-block"><i class="fas fa-undo mr-2"></i>Reset Form</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>

<script>
<?php if(isset($_SESSION['success_message'])): ?>
    Swal.fire({ icon: 'success', title: 'Berhasil!', text: '<?php echo $_SESSION['success_message']; ?>' });
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>
</script>
</body>
</html>