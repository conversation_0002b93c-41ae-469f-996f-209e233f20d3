<?php
// Memuat file-file yang diperlukan
require 'koneksi.php'; // Pastikan path ini benar
require_once 'includes/session_manager.php'; // Pastikan path ini benar
safe_session_start();

// Cek apakah metode request adalah POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // 1. Ambil dan bersihkan data dari form
    $username = trim($_POST['username']);
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $password = trim($_POST['password']);
    $confirm_password = trim($_POST['confirm_password']);

    // 2. Lakukan Validasi Sederhana di sisi server
    if (empty($username) || empty($first_name) || empty($password) || empty($confirm_password)) {
        $_SESSION['error'] = "Semua kolom wajib diisi!";
        header("Location: register.php");
        exit();
    }

    if (strlen($password) < 6) {
        $_SESSION['error'] = "Password minimal harus 6 karakter.";
        header("Location: register.php");
        exit();
    }

    if ($password !== $confirm_password) {
        $_SESSION['error'] = "Password dan Konfirmasi Password tidak cocok!";
        header("Location: register.php");
        exit();
    }

    // 3. Cek apakah username sudah ada di database
    $stmt = mysqli_prepare($koneksi, "SELECT id FROM users WHERE username = ?");
    mysqli_stmt_bind_param($stmt, "s", $username);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);

    if (mysqli_stmt_num_rows($stmt) > 0) {
        $_SESSION['error'] = "Username sudah terdaftar. Silakan gunakan username lain.";
        
        // Cleanup sebelum keluar
        mysqli_stmt_close($stmt);
        mysqli_close($koneksi);

        header("Location: register.php");
        exit();
    }
    // Tutup statement pengecekan duplikat karena sudah tidak diperlukan lagi
    mysqli_stmt_close($stmt);

    // 4. Hash password sebelum disimpan ke database
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // 5. Simpan data user baru ke database
    $role = 'client'; 
    $stmt = mysqli_prepare($koneksi, "INSERT INTO users (username, first_name, last_name, password, role) VALUES (?, ?, ?, ?, ?)");
    mysqli_stmt_bind_param($stmt, "sssss", $username, $first_name, $last_name, $hashed_password, $role);

    if (mysqli_stmt_execute($stmt)) {
        // Jika pendaftaran berhasil
        $_SESSION['success_message'] = "Pendaftaran berhasil! Silakan login.";
        
        // PERBAIKAN: Tutup statement dan koneksi SEBELUM redirect
        mysqli_stmt_close($stmt);
        mysqli_close($koneksi);

        header("Location: index.php");
        exit();
    } else {
        // Jika pendaftaran gagal
        $_SESSION['error'] = "Terjadi kesalahan. Gagal mendaftar.";

        // PERBAIKAN: Tutup statement dan koneksi SEBELUM redirect
        mysqli_stmt_close($stmt);
        mysqli_close($koneksi);
        
        header("Location: register.php");
        exit();
    }

} else {
    // Jika file diakses langsung tanpa metode POST, redirect ke halaman register
    header("Location: register.php");
    exit();
}
?>