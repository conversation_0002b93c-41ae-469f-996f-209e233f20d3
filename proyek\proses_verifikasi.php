<?php
require_once '../includes/session_manager.php';
// 1. Perbaikan kecil: Gunakan array untuk konsistensi
check_session_auth(['proyek']);
require_once '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $item_id = (int)$_POST['item_id'];
    $item_type = $_POST['item_type'];
    $status_verifikasi = $_POST['status_verifikasi'];
    $catatan = trim($_POST['catatan']);
    $verifikator_id = $_SESSION['id_petugas'];

    // Validasi input
    if (!in_array($item_type, ['tugas', 'file']) || !in_array($status_verifikasi, ['approved', 'rejected'])) {
        $_SESSION['error_message'] = "Data verifikasi tidak valid!";
        header("Location: verifikasi.php");
        exit;
    }

    // Tentukan nama tabel
    $table_name = ($item_type == 'tugas') ? 'tugas_proyek' : 'file_gambar';

    // Mulai transaction (ini sudah bagus!)
    mysqli_begin_transaction($koneksi);

    try {
        // 2. PERBAIKAN UTAMA: Gunakan Prepared Statement untuk UPDATE
        $sql_update = "UPDATE $table_name SET 
                        status_verifikasi = ?,
                        tanggal_verifikasi = NOW(),
                        verifikator_id = ?,
                        catatan_verifikasi = ?
                       WHERE id = ?";
        
        $stmt_update = mysqli_prepare($koneksi, $sql_update);
        mysqli_stmt_bind_param($stmt_update, "sisi", $status_verifikasi, $verifikator_id, $catatan, $item_id);

        if (!mysqli_stmt_execute($stmt_update)) {
            throw new Exception("Gagal update status verifikasi.");
        }
        mysqli_stmt_close($stmt_update);


        // 3. PERBAIKAN UTAMA: Gunakan Prepared Statement untuk INSERT LOG
        $sql_log = "INSERT INTO verifikasi_log 
                      (tipe, item_id, status_lama, status_baru, verifikator_id, catatan) 
                    VALUES (?, ?, 'pending', ?, ?, ?)";
        
        $stmt_log = mysqli_prepare($koneksi, $sql_log);
        mysqli_stmt_bind_param($stmt_log, "sisss", $item_type, $item_id, $status_verifikasi, $verifikator_id, $catatan);
        
        if (!mysqli_stmt_execute($stmt_log)) {
            throw new Exception("Gagal menyimpan log verifikasi.");
        }
        mysqli_stmt_close($stmt_log);

        // Commit transaction
        mysqli_commit($koneksi);

        // 4. Perbaikan: Gunakan Session untuk notifikasi agar lebih rapi
        $status_text = ($status_verifikasi == 'approved') ? 'disetujui' : 'ditolak';
        $item_text = ($item_type == 'tugas') ? 'Tugas' : 'File';
        $_SESSION['success_message'] = "$item_text berhasil $status_text!";

    } catch (Exception $e) {
        mysqli_rollback($koneksi);
        $_SESSION['error_message'] = "Terjadi kesalahan: " . $e->getMessage();
    }

    mysqli_close($koneksi);
    header("Location: verifikasi.php");
    exit;

} else {
    header("Location: verifikasi.php");
    exit;
}
?>