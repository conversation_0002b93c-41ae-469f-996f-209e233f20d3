<?php
require 'koneksi.php';
require_once 'includes/session_manager.php';
safe_session_start();

$user = trim($_POST['username']);
$pass = trim($_POST['password']);

// -- Cek di tabel petugas --
$stmt_petugas = mysqli_prepare($koneksi, "SELECT id_petugas, nama_petugas, password, level FROM petugas WHERE username = ?");
mysqli_stmt_bind_param($stmt_petugas, "s", $user);
mysqli_stmt_execute($stmt_petugas);
$result_petugas = mysqli_stmt_get_result($stmt_petugas);

if (mysqli_num_rows($result_petugas) > 0) {
    $data = mysqli_fetch_assoc($result_petugas);

    $level = $data['level'];
    $password_db = $data['password'];
    $is_verified = false;

    if ($level == 'proyek') {
        // Login proyek tanpa hash
        $is_verified = ($pass === $password_db);
    } elseif ($level == 'admin') {
        // Login admin menggunakan hash
        $is_verified = password_verify($pass, $password_db);
    }

    if ($is_verified) {
        session_regenerate_id(true);
        $_SESSION['id_petugas'] = $data['id_petugas'];
        $_SESSION['user'] = $user;
        $_SESSION['nama'] = $data['nama_petugas'];
        $_SESSION['level'] = $level;

        if ($level == "proyek") {
            header('Location: proyek/proyek.php');
            exit();
        } elseif ($level == "admin") {
            header('Location: admin/admin.php');
            exit();
        }
    } else {
        echo "<script>alert('Username atau Password tidak ditemukan'); window.location='index.php';</script>";
        exit();
    }

} else {
    // -- Jika bukan petugas, cek di tabel users (client) --
    $stmt_client = mysqli_prepare($koneksi, "SELECT id, first_name, last_name, password FROM users WHERE username = ? AND role = 'client'");
    mysqli_stmt_bind_param($stmt_client, "s", $user);
    mysqli_stmt_execute($stmt_client);
    $result_client = mysqli_stmt_get_result($stmt_client);

    if (mysqli_num_rows($result_client) > 0) {
        $data = mysqli_fetch_assoc($result_client);

        if (password_verify($pass, $data['password'])) {
            session_regenerate_id(true); 

            $_SESSION['id_client'] = $data['id'];
            $_SESSION['user'] = $user;
            $_SESSION['nama'] = $data['first_name'] . ' ' . $data['last_name'];
            $_SESSION['level'] = 'client';

            header('Location: client/client.php');
            exit();
        } else {
            echo "<script>alert('Username atau Password tidak ditemukan'); window.location='index.php';</script>";
            exit();
        }
    } else {
        echo "<script>alert('Username atau Password tidak ditemukan'); window.location='index.php';</script>";
        exit();
    }

    // Menutup statement client
    mysqli_stmt_close($stmt_client);
}

// Menutup statement petugas dan koneksi
mysqli_stmt_close($stmt_petugas);
mysqli_close($koneksi);
?>
