<?php
require_once '../includes/session_manager.php';
// <PERSON>ya tim proyek yang boleh mengubah status tugas
check_session_auth(['proyek']);
require_once '../koneksi.php';

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['tugas_id']) && isset($_POST['status'])) {
    
    $tugas_id = (int)$_POST['tugas_id'];
    $status_baru = $_POST['status'];

    // Validasi sederhana untuk status
    if (!in_array($status_baru, ['proses', 'selesai', 'batal'])) {
        $_SESSION['error_message'] = "Status yang dipilih tidak valid.";
        header("Location: riwayat_verifikasi.php"); // Diarahkan ke halaman yang benar
        exit();
    }

    // Siapkan perintah UPDATE yang aman
    $stmt = mysqli_prepare($koneksi, "UPDATE tugas_proyek SET status = ? WHERE id = ?");
    mysqli_stmt_bind_param($stmt, "si", $status_baru, $tugas_id);

    // Eksekusi dan berikan feedback
    if (mysqli_stmt_execute($stmt)) {
        $_SESSION['success_message_riwayat'] = "Status tugas berhasil diubah!";
    } else {
        $_SESSION['error_message_riwayat'] = "Gagal mengubah status tugas.";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($koneksi);
    
    // 📝 PERBAIKAN UTAMA: Kembali ke halaman kelola proyek
    header("Location: riwayat_verifikasi.php");
    exit();

} else {
    // Jika data tidak lengkap atau diakses langsung, kembalikan
    $_SESSION['error_message'] = "Permintaan tidak valid.";
    header("Location: riwayat_verifikasi.php");
    exit();
}
?>