<?php
require_once '../includes/session_manager.php';
check_session_auth(['admin', 'proyek']);
require_once '../koneksi.php';

$page_title = "Laporan Progress Proyek";
include 'includes/header/header.php';

$user_level = $_SESSION['level'];
$client_id_filter = null;
$clients = [];

if ($user_level != 'client') {
    $result_clients = mysqli_query($koneksi, "SELECT id, first_name, last_name FROM users WHERE role = 'client' ORDER BY first_name");
    while ($row = mysqli_fetch_assoc($result_clients)) {
        $clients[] = $row;
    }
    if (isset($_GET['client_id']) && !empty($_GET['client_id'])) {
        $client_id_filter = (int)$_GET['client_id'];
    }
} else {
    $client_id_filter = $_SESSION['id_client'];
}
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">
            <h1 class="h3 mb-4 text-gray-800">Laporan Progress Proyek</h1>

            <?php if ($user_level != 'client'): ?>
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <label for="client_id">Tampilkan Progress untuk Client:</label>
                                <select name="client_id" id="client_id" class="form-control" onchange="this.form.submit()">
                                    <option value="">-- Semua Client --</option>
                                    <?php foreach($clients as $client): ?>
                                        <option value="<?php echo $client['id']; ?>" <?php if($client_id_filter == $client['id']) echo 'selected'; ?>>
                                            <?php echo htmlspecialchars($client['first_name'].' '.$client['last_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <a href="progress_proyek.php" class="btn btn-secondary">Reset Filter</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-list-alt mr-2"></i>Detail Progress Proyek</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="dataTable" width="100%" cellspacing="0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Proyek / Tugas</th>
                                    <?php if ($user_level != 'client') echo '<th>Client</th>'; ?>
                                    <th style="width: 25%;">Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Query untuk mengambil proyek
                                $sql_proyek = "SELECT p.id, p.nama_proyek, u.first_name, u.last_name FROM proyek p JOIN users u ON p.client_id = u.id";
                                if ($client_id_filter) { $sql_proyek .= " WHERE p.client_id = ?"; }
                                $sql_proyek .= " ORDER BY p.nama_proyek ASC";
                                
                                $stmt_proyek = mysqli_prepare($koneksi, $sql_proyek);
                                if ($client_id_filter) { mysqli_stmt_bind_param($stmt_proyek, "i", $client_id_filter); }
                                mysqli_stmt_execute($stmt_proyek);
                                $result_proyek = mysqli_stmt_get_result($stmt_proyek);

                                if (mysqli_num_rows($result_proyek) > 0):
                                    while ($proyek = mysqli_fetch_assoc($result_proyek)):
                                        // Hitung progress untuk proyek ini
                                        $stmt_progress = mysqli_prepare($koneksi, "
                                            SELECT COUNT(id) AS total, SUM(CASE WHEN status = 'selesai' THEN 1 ELSE 0 END) AS selesai
                                            FROM tugas_proyek WHERE proyek_id = ? AND status_verifikasi = 'approved'");
                                        mysqli_stmt_bind_param($stmt_progress, "i", $proyek['id']);
                                        mysqli_stmt_execute($stmt_progress);
                                        $progress = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt_progress));
                                        $percentage = ($progress['total'] > 0) ? ($progress['selesai'] / $progress['total']) * 100 : 0;
                                ?>
                                    <tr class="bg-gray-200">
                                        <td><strong><?php echo htmlspecialchars($proyek['nama_proyek']); ?></strong></td>
                                        <?php if ($user_level != 'client') echo '<td><strong>'.htmlspecialchars($proyek['first_name'].' '.$proyek['last_name']).'</strong></td>'; ?>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo $percentage; ?>%;" aria-valuenow="<?php echo $percentage; ?>">
                                                    <?php echo round($percentage); ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <?php
                                    $stmt_tugas = mysqli_prepare($koneksi, "SELECT nama_kegiatan, status FROM tugas_proyek WHERE proyek_id = ? AND status_verifikasi = 'approved' ORDER BY tgl_mulai ASC");
                                    mysqli_stmt_bind_param($stmt_tugas, "i", $proyek['id']);
                                    mysqli_stmt_execute($stmt_tugas);
                                    $result_tugas = mysqli_stmt_get_result($stmt_tugas);
                                    while($tugas = mysqli_fetch_assoc($result_tugas)):
                                        $status_class = 'secondary';
                                        if ($tugas['status'] == 'proses') { $status_class = 'warning'; } 
                                        elseif ($tugas['status'] == 'selesai') { $status_class = 'success'; } 
                                        elseif ($tugas['status'] == 'batal') { $status_class = 'danger'; }
                                    ?>
                                    <tr>
                                        <td class="pl-4"><i class="fas fa-angle-right mr-2 text-gray-400"></i><?php echo htmlspecialchars($tugas['nama_kegiatan']); ?></td>
                                        <?php if ($user_level != 'client') echo '<td></td>'; // Kolom client kosong untuk baris tugas ?>
                                        <td>
                                            <span class="badge badge-<?php echo $status_class; ?>"><?php echo ucfirst($tugas['status']); ?></span>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>

                                <?php endwhile; else: ?>
                                <tr>
                                    <td colspan="<?php echo ($user_level != 'client') ? '4' : '3'; ?>" class="text-center py-4">Tidak ada proyek untuk ditampilkan.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>
</body>
</html>