<ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

    <style>
        .sidebar .nav-item .collapse .collapse-inner .dropdown-item.active:hover,
        .sidebar .nav-item .collapse .collapse-inner .dropdown-item.bg-primary:hover {
            background-color: #eaecf4 !important; /* Warna hover abu-abu (default template) */
            color: #3a3b45 !important; /* Warna teks hitam saat hover (default template) */
        }
    </style>

    <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
        <div class="sidebar-brand-icon rotate-n-15">
            <i class="fas fa-drafting-compass"></i>
        </div>
        <div class="sidebar-brand-text mx-3">Antosa Arsitek</div>
    </a>

    <hr class="sidebar-divider my-0">

    <li class="nav-item <?php echo (basename($_SERVER['PHP_SELF']) == 'proyek.php') ? 'active' : ''; ?>">
        <a class="nav-link" href="proyek.php">
            <i class="fas fa-fw fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>

    <hr class="sidebar-divider">

    <div class="sidebar-heading">
        Manajemen Proyek
    </div>

    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTugas"
           aria-expanded="<?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_proyek.php', 'input_tugas.php', 'tugas_harian.php', 'progress_proyek.php'])) ? 'true' : 'false'; ?>"
           aria-controls="collapseTugas">
            <i class="fas fa-fw fa-folder-open"></i>
            <span>Manajemen Proyek</span>
        </a>
        <div id="collapseTugas" class="collapse <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['kelola_proyek.php', 'input_tugas.php', 'tugas_harian.php', 'progress_proyek.php'])) ? 'show' : ''; ?>"
             aria-labelledby="headingTugas" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Kelola Proyek & Tugas:</h6>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'kelola_proyek.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="kelola_proyek.php">
                    <i class="fas fa-folder-plus mr-2"></i> Input Proyek
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'input_tugas.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="input_tugas.php">
                    <i class="fas fa-plus-circle mr-2"></i> Input Tugas
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'tugas_harian.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="tugas_harian.php">
                    <i class="fas fa-list-alt mr-2"></i> Daftar Tugas
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'progress_proyek.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="progress_proyek.php">
                    <i class="fas fa-chart-line mr-2"></i> Progres Proyek
                </a>
            </div>
        </div>
    </li>

    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseFile"
           aria-expanded="<?php echo (in_array(basename($_SERVER['PHP_SELF']), ['upload_file.php', 'file_approved.php'])) ? 'true' : 'false'; ?>"
           aria-controls="collapseFile">
            <i class="fas fa-fw fa-images"></i>
            <span>Manajemen File</span>
        </a>
        <div id="collapseFile" class="collapse <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['upload_file.php', 'file_approved.php'])) ? 'show' : ''; ?>"
             aria-labelledby="headingFile" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Kelola File:</h6>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'upload_file.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="upload_file.php">
                    <i class="fas fa-upload mr-2"></i> Upload File Desain
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'file_approved.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="file_approved.php">
                    <i class="fas fa-check-circle mr-2"></i> File Disetujui
                </a>
            </div>
        </div>
    </li>
    
    <hr class="sidebar-divider">

    <div class="sidebar-heading">
        Verifikasi & Review
    </div>

    <li class="nav-item">
        <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseVerifikasi"
           aria-expanded="<?php echo (in_array(basename($_SERVER['PHP_SELF']), ['verifikasi.php', 'riwayat_verifikasi.php', 'laporan_tugas_selesai.php', 'review_revisi.php', 'kelola_rab.php'])) ? 'true' : 'false'; ?>"
           aria-controls="collapseVerifikasi">
            <i class="fas fa-fw fa-clipboard-check"></i>
            <span>Verifikasi & Review</span>
        </a>
        <div id="collapseVerifikasi" class="collapse <?php echo (in_array(basename($_SERVER['PHP_SELF']), ['verifikasi.php', 'riwayat_verifikasi.php', 'laporan_tugas_selesai.php', 'review_revisi.php', 'kelola_rab.php'])) ? 'show' : ''; ?>"
             aria-labelledby="headingVerifikasi" data-parent="#accordionSidebar">
            <div class="bg-white py-2 collapse-inner rounded">
                <h6 class="collapse-header">Kelola Review:</h6>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'verifikasi.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="verifikasi.php">
                    <i class="fas fa-clipboard-check mr-2"></i> Verifikasi & Approval
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'riwayat_verifikasi.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="riwayat_verifikasi.php">
                    <i class="fas fa-history mr-2"></i> Tugas Proyek
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'laporan_tugas_selesai.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="laporan_tugas_selesai.php">
                    <i class="fas fa-tasks mr-2"></i> Riwayat Project
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'review_revisi.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="review_revisi.php">
                    <i class="fas fa-edit mr-2"></i> Review Revisi
                </a>
                <a class="dropdown-item <?php echo (basename($_SERVER['PHP_SELF']) == 'kelola_rab.php') ? 'active bg-primary text-white' : ''; ?>"
                   href="kelola_rab.php">
                    <i class="fas fa-calculator mr-2"></i> Kelola RAB
                </a>
            </div>
        </div>
    </li>

    <hr class="sidebar-divider d-none d-md-block">

    <div class="sidebar-heading">
        Sistem
    </div>

    <li class="nav-item">
        <a class="nav-link" href="#" data-toggle="modal" data-target="#logoutModal">
            <i class="fas fa-sign-out-alt"></i>
            <span>Keluar</span>
        </a>
    </li>

    <div class="text-center d-none d-md-inline">
        <button class="rounded-circle border-0" id="sidebarToggle"></button>
    </div>
</ul>