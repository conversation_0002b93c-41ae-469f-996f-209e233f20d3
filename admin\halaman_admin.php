<?php
/**
 * <PERSON>aman Admin - Dashboard Content
 *
 * Note: Session validation is handled by admin.php that includes this file.
 * This file assumes session is already validated and $_SESSION data is available.
 */

if (isset($_GET['url']))
{
    $url=$_GET['url'];

    switch($url)
    {
        // Bagian ini dipertahankan sesuai permintaan Anda
        default:
            // Redirect ke dashboard jika URL tidak dikenali
            echo "<script>window.location='admin.php';</script>";
            break;
    }
}
else
{
    ?>
    <div class="row">
        <div class="col-xl-12 col-md-12 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Selamat Datang
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php
                                // Safety check for session data
                                echo isset($_SESSION['nama']) && !empty($_SESSION['nama']) ? htmlspecialchars($_SESSION['nama']) : 'Admin';
                                ?> - Admin Antosa Arsitek
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <?php
        require '../koneksi.php';

        // Mengambil semua statistik dengan satu query yang efisien
        $stats = [
            'total_client' => 0, 'tugas_pending' => 0,
            'file_approved' => 0, 'rab_draft' => 0
        ];
        $sql_stats = "SELECT 
                        (SELECT COUNT(*) FROM users WHERE role='client') as total_client,
                        (SELECT COUNT(*) FROM tugas_proyek WHERE status_verifikasi='pending') as tugas_pending,
                        (SELECT COUNT(*) FROM file_gambar WHERE status_verifikasi='approved') as file_approved,
                        (SELECT COUNT(*) FROM rab_proyek WHERE status='draft') as rab_draft
                    ";
        $result_stats = mysqli_query($koneksi, $sql_stats);
        if ($result_stats) {
            $stats = mysqli_fetch_assoc($result_stats);
        }
        ?>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Client</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total_client']; ?></div>
                        </div>
                        <div class="col-auto"><i class="fas fa-user-tie fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Tugas Pending</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['tugas_pending']; ?></div>
                        </div>
                        <div class="col-auto"><i class="fas fa-clock fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">File Approved</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['file_approved']; ?></div>
                        </div>
                        <div class="col-auto"><i class="fas fa-check fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">RAB Draft</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['rab_draft']; ?></div>
                        </div>
                        <div class="col-auto"><i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Kelola User Proyek</div>
                            <div class="text-gray-600 small">Tambah, edit, atau hapus user proyek</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_user_proyek.php" class="btn btn-info btn-sm"><i class="fas fa-users"></i> Kelola</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Kelola Client</div>
                            <div class="text-gray-600 small">Tambah, edit, atau hapus client</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_client.php" class="btn btn-success btn-sm"><i class="fas fa-user-tie"></i> Kelola</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Kelola Proyek</div>
                            <div class="text-gray-600 small">Buat & lihat daftar proyek untuk client</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_proyek.php" class="btn btn-warning btn-sm"><i class="fas fa-folder-plus"></i> Kelola</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Kelola File Gambar</div>
                            <div class="text-gray-600 small">Verifikasi file gambar/desain</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_file_gambar.php" class="btn btn-primary btn-sm"><i class="fas fa-images"></i> Kelola</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Kelola RAB Proyek</div>
                            <div class="text-gray-600 small">Approve/reject RAB proyek</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_rab_proyek.php" class="btn btn-secondary btn-sm"><i class="fas fa-file-invoice-dollar"></i> Kelola</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Revisi Request</div>
                            <div class="text-gray-600 small">Response permintaan revisi client</div>
                        </div>
                        <div class="col-auto">
                            <a href="kelola_revisi_request.php" class="btn btn-danger btn-sm"><i class="fas fa-edit"></i> Response</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}
?>