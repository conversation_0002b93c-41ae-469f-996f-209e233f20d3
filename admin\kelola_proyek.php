<?php
require_once '../includes/session_manager.php';
check_session_auth('admin');
require_once '../koneksi.php';

// Proses simpan proyek baru jika ada request POST
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['nama_proyek'])) {
    $nama_proyek = trim($_POST['nama_proyek']);
    $deskripsi = trim($_POST['deskripsi']);
    $client_id = $_POST['client_id'];

    if (!empty($nama_proyek) && !empty($client_id)) {
        $stmt = mysqli_prepare($koneksi, "INSERT INTO proyek (client_id, nama_proyek, deskripsi) VALUES (?, ?, ?)");
        mysqli_stmt_bind_param($stmt, "iss", $client_id, $nama_proyek, $deskripsi);
        if(mysqli_stmt_execute($stmt)) {
            $_SESSION['success_message'] = "Proyek baru berhasil ditambahkan!";
        } else {
            $_SESSION['error_message'] = "Gagal menambahkan proyek.";
        }
        mysqli_stmt_close($stmt);
        header("Location: kelola_proyek.php");
        exit();
    }
}

$page_title = "Kelola Proyek";
include 'includes/header/header.php';

// Ambil daftar client untuk dropdown
$clients = [];
$result_clients = mysqli_query($koneksi, "SELECT id, first_name, last_name FROM users WHERE role = 'client'");
while ($row = mysqli_fetch_assoc($result_clients)) {
    $clients[] = $row;
}
?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">
            <h1 class="h3 mb-4 text-gray-800">Kelola Proyek</h1>

            <div class="row">
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Tambah Proyek Baru</h6>
                        </div>
                        <div class="card-body">
                            <form action="kelola_proyek.php" method="POST">
                                <div class="form-group">
                                    <label for="nama_proyek">Nama Proyek</label>
                                    <input type="text" class="form-control" name="nama_proyek" required>
                                </div>
                                <div class="form-group">
                                    <label for="client_id">Untuk Client</label>
                                    <select class="form-control" name="client_id" required>
                                        <option value="">-- Pilih Client --</option>
                                        <?php foreach($clients as $client): ?>
                                            <option value="<?php echo $client['id']; ?>">
                                                <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="deskripsi">Deskripsi Singkat</label>
                                    <textarea class="form-control" name="deskripsi" rows="3"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Simpan Proyek</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Daftar Proyek</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Nama Proyek</th>
                                        <th>Client</th>
                                        <th>Status</th>
                                        <th>Tgl. Dibuat</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql_proyek = mysqli_query($koneksi, 
                                        "SELECT p.*, u.first_name, u.last_name 
                                         FROM proyek p 
                                         JOIN users u ON p.client_id = u.id 
                                         ORDER BY p.created_at DESC");
                                    if(mysqli_num_rows($sql_proyek) > 0) {
                                        while($proyek = mysqli_fetch_assoc($sql_proyek)) {
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($proyek['nama_proyek']); ?></td>
                                        <td><?php echo htmlspecialchars($proyek['first_name'].' '.$proyek['last_name']); ?></td>
                                        <td><span class="badge badge-info"><?php echo ucfirst($proyek['status']); ?></span></td>
                                        <td><?php echo date('d M Y', strtotime($proyek['created_at'])); ?></td>
                                    </tr>
                                    <?php } } else { ?>
                                    <tr><td colspan="4" class="text-center">Belum ada proyek.</td></tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>

<script>
<?php if(isset($_SESSION['success_message'])): ?>
    Swal.fire({ icon: 'success', title: 'Berhasil!', text: '<?php echo $_SESSION['success_message']; ?>' });
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>
</script>
</body>
</html>