    <?php
    require_once 'includes/session_manager.php';
    safe_session_start();
    ?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title>Register - Antosa Arsitek</title>

        <link href="tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <link href="tmp/css/sb-admin-2.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <style>
            /* Menggunakan CSS yang sama persis dengan index.php untuk konsistensi */
            body {
                font-family: 'Inter', sans-serif;
                background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 50%, #1a1a1a 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 2rem 0;
            }
            .login-container {
                background: rgba(255, 255, 255, 0.03);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.08);
                border-radius: 24px;
                padding: 3.5rem;
                width: 100%;
                max-width: 480px; /* Sedikit lebih lebar untuk form register */
                box-shadow: 0 32px 64px rgba(0, 0, 0, 0.2);
            }
            .logo-section { 
                text-align: center; 
                margin-bottom: 2rem; 
            }

            .logo-icon { 
                width: 80px; 
                height: 80px; 
                margin: 0 auto 1.5rem; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                position: relative; 
            }

            .logo-icon i { 
                font-size: 3.5rem; 
                color: #fff;
                opacity: 0.9; 
            }

            .brand-title { 
                color: #fff; 
                font-size: 2rem; 
                font-weight: 700; 
                margin: 0; 
                letter-spacing: -0.5px; 
            }

            .brand-subtitle { 
                color: rgba(255, 255, 255, 0.7); 
                font-size: 0.875rem; 
                font-weight: 400; 
                margin: 0.5rem 0 0 0; 
                letter-spacing: 2px; 
                text-transform: uppercase; 
            }

            .form-group { 
                margin-bottom: 1.2rem; 
                position: relative; 
            }

            .form-control-modern { 
                background: rgba(255, 255, 255, 0.1); 
                border: 1px solid rgba(255, 255, 255, 0.2); 
                border-radius: 12px; 
                color: #fff; 
                font-size: 1rem; 
                padding: 1rem 1.25rem; 
                transition: all 0.3s ease; 
                backdrop-filter: blur(10px); 
                width: 100%;
            }

            .form-control-modern::placeholder { 
                color: rgba(255, 255, 255, 0.6); 
                font-weight: 400; 
            }

            .form-control-modern:focus { 
                background: rgba(255, 255, 255, 0.15); 
                border-color: rgba(255, 255, 255, 0.4); 
                box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.1); 
                color: #fff; 
            }

            .password-toggle { 
                position: absolute; 
                right: 1rem; 
                top: 50%; 
                transform: translateY(-50%); 
                background: none; 
                border: none; 
                color: rgba(255, 255, 255, 0.6); 
                cursor: pointer; 
                font-size: 1.1rem; 
                transition: color 0.3s ease; 
            }

            .password-toggle:hover { 
                color: rgba(255, 255, 255, 0.9);
             }

            .btn-submit { 
                background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); 
                border: none; 
                border-radius: 12px; 
                color: #fff; 
                font-size: 1rem; 
                font-weight: 600; 
                padding: 1rem; 
                width: 100%; 
                margin-top: 1.5rem; 
                transition: all 0.3s ease; 
                box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3); }

            .btn-submit:hover { 
                background: linear-gradient(135deg, #357abd 0%, #2968a3 100%); 
                transform: translateY(-2px); 
                box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3); 
                color: #fff; }

            .footer-text { 
                text-align: center; 
                color: rgba(255, 255, 255, 0.5); 
                font-size: 0.875rem; 
                margin-top: 2rem; }
                
        </style>
    </head>

    <body>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-container">
                        
                        <div class="logo-section">
                            <div class="logo-icon">
                                <i class="fas fa-drafting-compass"></i></div>
                            <h1 class="brand-title">BUAT AKUN BARU</h1>
                            <p class="brand-subtitle">Antosa Arsitek</p>
                        </div>

                        <form method="post" action="register_process.php">
                            <div class="form-group">
                                <input type="text" class="form-control form-control-modern" name="username" placeholder="Username" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <input type="text" class="form-control form-control-modern" name="first_name" placeholder="Nama Depan" required>
                                </div>
                                <div class="col-md-6 form-group">
                                    <input type="text" class="form-control form-control-modern" name="last_name" placeholder="Nama Belakang" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control form-control-modern" name="password" id="password" placeholder="Password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('password', 'toggleIcon1')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control form-control-modern" name="confirm_password" id="confirm_password" placeholder="Konfirmasi Password" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('confirm_password', 'toggleIcon2')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>

                            <button type="submit" class="btn-submit">
                                Daftar Sekarang
                            </button>
                        </form>

                        <p class="mt-4 text-center">
                            <span style="color: rgba(255,255,255,0.7);">Sudah punya akun?</span>
                            <a href="index.php" style="color: #4a90e2; text-decoration: none; font-weight: 600;">Login di sini</a>
                        </p>

                        <div class="footer-text">
                            © 2025 Antosa Architect
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="tmp/vendor/jquery/jquery.min.js"></script>
        <script src="tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
        <script src="tmp/vendor/jquery-easing/jquery.easing.min.js"></script>
        <script src="tmp/js/sb-admin-2.min.js"></script>

        <script>
            function togglePassword(fieldId, iconId) {
                const passwordField = document.getElementById(fieldId);
                const toggleIcon = document.getElementById(iconId);

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleIcon.classList.remove('fa-eye');
                    toggleIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    toggleIcon.classList.remove('fa-eye-slash');
                    toggleIcon.classList.add('fa-eye');
                }
            }
        </script>

        <?php if (isset($_SESSION['error'])): ?>
        <script>
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '<?php echo $_SESSION['error']; ?>',
                background: '#333',
                color: '#fff'
            });
        </script>
        <?php unset($_SESSION['error']); endif; ?>

    </body>
    </html>