<?php
require_once 'includes/session_manager.php';
safe_session_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A<PERSON><PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a1a1a;
            color: #e2e8f0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            text-align: center;
        }
        .container {
            max-width: 500px;
            padding: 2rem;
        }
        h1 {
            font-size: 4rem;
            font-weight: 700;
            color: #e53e3e; /* Merah */
            margin: 0;
        }
        h2 {
            font-size: 1.5rem;
            margin: 1rem 0;
            color: #cbd5e0;
        }
        p {
            color: #a0aec0;
        }
        a {
            display: inline-block;
            margin-top: 1.5rem;
            padding: 0.75rem 1.5rem;
            background-color: #3182ce; /* Biru */
            color: #fff;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        a:hover {
            background-color: #2b6cb0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>403</h1>
        <h2>Akses Ditolak</h2>
        <p>Anda tidak memiliki izin untuk mengakses halaman yang Anda minta.</p>
        
        <?php 
            // Cek level user dan arahkan ke halaman yang sesuai
            $dashboard_link = "index.php"; // Default jika tidak ada session
            if(isset($_SESSION['level'])) {
                if($_SESSION['level'] == 'admin') {
                    $dashboard_link = "admin/admin.php";
                } else if ($_SESSION['level'] == 'proyek') {
                    $dashboard_link = "proyek/proyek.php";
                } else if ($_SESSION['level'] == 'client') {
                    $dashboard_link = "client/client.php";
                }
            }
        ?>
        <a href="<?php echo $dashboard_link; ?>">Kembali ke Halaman Utama</a>
    </div>
</body>
</html>