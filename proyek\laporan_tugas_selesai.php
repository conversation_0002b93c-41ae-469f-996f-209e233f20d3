<?php
require_once '../includes/session_manager.php';
// Admin dan Proyek bisa melihat halaman ini
check_session_auth(['admin', 'proyek']);
require_once '../koneksi.php';

$page_title = "<PERSON>poran <PERSON> Selesai";
include 'includes/header/header.php';

// Ambil daftar client untuk filter
$clients = [];
$result_clients = mysqli_query($koneksi, "SELECT id, first_name, last_name FROM users WHERE role = 'client' ORDER BY first_name");
while ($row = mysqli_fetch_assoc($result_clients)) {
    $clients[] = $row;
}

// Ambil nilai filter dari URL jika ada
$client_id_filter = isset($_GET['client_id']) ? (int)$_GET['client_id'] : null;
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">

            <h1 class="h3 mb-2 text-gray-800">Riwayat Semua Tugas</h1>
            <p class="mb-4">Halaman ini menampilkan riwayat semua tugas proyek yang telah selesai dikerjakan dan disetujui.</p>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-filter mr-2"></i>Filter Laporan</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <label for="client_id">Tampilkan Tugas untuk Client:</label>
                                <select name="client_id" id="client_id" class="form-control">
                                    <option value="">-- Semua Client --</option>
                                    <?php foreach($clients as $client): ?>
                                        <option value="<?php echo $client['id']; ?>" <?php if($client_id_filter == $client['id']) echo 'selected'; ?>>
                                            <?php echo htmlspecialchars($client['first_name'].' '.$client['last_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-search mr-2"></i>Tampilkan</button>
                                <a href="laporan_tugas_selesai.php" class="btn btn-secondary">Reset</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-check-circle mr-2"></i>Riwayat Tugas Selesai</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="text-center">No</th>
                                    <th>Nama Proyek</th>
                                    <th>Nama Tugas</th>
                                    <th>Untuk Client</th>
                                    <th class="text-center">Timeline Pengerjaan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Query dasar untuk mengambil tugas yang sudah selesai
                                $sql = "
                                    SELECT 
                                        tp.nama_kegiatan, tp.deskripsi, tp.tgl_mulai, tp.tgl_selesai,
                                        p.nama_proyek,
                                        u.first_name, u.last_name
                                    FROM tugas_proyek tp
                                    JOIN proyek p ON tp.proyek_id = p.id
                                    JOIN users u ON p.client_id = u.id
                                    WHERE tp.status_verifikasi = 'approved' AND tp.status = 'selesai'";
                                
                                // Tambahkan filter client jika dipilih
                                if ($client_id_filter) {
                                    $sql .= " AND p.client_id = ?";
                                }
                                
                                $sql .= " ORDER BY tp.tgl_selesai DESC";
                                
                                $stmt = mysqli_prepare($koneksi, $sql);

                                if ($client_id_filter) {
                                    mysqli_stmt_bind_param($stmt, "i", $client_id_filter);
                                }

                                mysqli_stmt_execute($stmt);
                                $result_tugas = mysqli_stmt_get_result($stmt);

                                if (mysqli_num_rows($result_tugas) > 0) {
                                    $no = 1;
                                    while ($tugas = mysqli_fetch_assoc($result_tugas)) {
                                ?>
                                <tr>
                                    <td class="text-center align-middle"><?php echo $no++; ?></td>
                                    <td class="align-middle"><?php echo htmlspecialchars($tugas['nama_proyek']); ?></td>
                                    <td class="align-middle">
                                        <strong><?php echo htmlspecialchars($tugas['nama_kegiatan']); ?></strong>
                                        <p class="text-muted small mb-0"><?php echo htmlspecialchars($tugas['deskripsi']); ?></p>
                                    </td>
                                    <td class="align-middle"><?php echo htmlspecialchars($tugas['first_name'] . ' ' . $tugas['last_name']); ?></td>
                                    <td class="text-center align-middle">
                                        <small><?php echo date('d M Y', strtotime($tugas['tgl_mulai'])); ?> - <?php echo date('d M Y', strtotime($tugas['tgl_selesai'])); ?></small>
                                    </td>
                                </tr>
                                <?php } } else { ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        Tidak ada data tugas selesai yang cocok dengan filter.
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include 'includes/footer/footer.php'; ?>
</div>
</body>
</html>