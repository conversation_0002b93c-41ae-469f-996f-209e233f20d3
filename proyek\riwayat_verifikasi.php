<?php
require_once '../includes/session_manager.php';
check_session_auth(['proyek', 'admin']);
require_once '../koneksi.php';

$page_title = "Riwayat Semua Tugas";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

<div id="content-wrapper" class="d-flex flex-column">
    <div id="content">
        <?php include 'includes/topbar/topbar.php'; ?>
        <div class="container-fluid">

            <h1 class="h3 mb-4 text-gray-800">Kelola Project</h1>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><i class="fas fa-book mr-2"></i>Log Semua Tugas Proyek</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="text-center">No</th>
                                    <th>Nama Proyek</th>
                                    <th>Nama Tugas</th>
                                    <th>Untuk Client</th>
                                    <th class="text-center">Timeline</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sql_tugas = mysqli_query($koneksi, "
                                    SELECT tp.*, p.nama_proyek, u.first_name, u.last_name
                                    FROM tugas_proyek tp
                                    LEFT JOIN proyek p ON tp.proyek_id = p.id
                                    LEFT JOIN users u ON p.client_id = u.id
                                    ORDER BY tp.tanggal_submit DESC
                                ");
                                if (mysqli_num_rows($sql_tugas) > 0) {
                                    $no = 1;
                                    while ($tugas = mysqli_fetch_assoc($sql_tugas)) {
                                ?>
                                <tr>
                                    <td class="text-center align-middle"><?php echo $no++; ?></td>
                                    <td class="align-middle"><?php echo htmlspecialchars($tugas['nama_proyek'] ?? 'N/A'); ?></td>
                                    <td class="align-middle">
                                        <strong><?php echo htmlspecialchars($tugas['nama_kegiatan']); ?></strong>
                                        <p class="text-muted small mb-0"><?php echo htmlspecialchars($tugas['deskripsi']); ?></p>
                                    </td>
                                    <td class="align-middle"><?php echo htmlspecialchars($tugas['first_name'] . ' ' . $tugas['last_name']); ?></td>
                                    <td class="text-center align-middle"><small><?php echo date('d M Y', strtotime($tugas['tgl_mulai'])); ?> - <?php echo date('d M Y', strtotime($tugas['tgl_selesai'])); ?></small></td>
                                    <td class="text-center align-middle">
                                        <?php
                                        // ... (logika badge status Anda sudah benar) ...
                                        $status_verifikasi = $tugas['status_verifikasi'];
                                        $status_pengerjaan = $tugas['status'];
                                        $badge_class = 'secondary'; $badge_text = 'Unknown';
                                        if ($status_verifikasi == 'pending') { $badge_class = 'warning'; $badge_text = 'Pending'; } 
                                        elseif ($status_verifikasi == 'rejected') { $badge_class = 'danger'; $badge_text = 'Ditolak'; } 
                                        elseif ($status_verifikasi == 'approved') {
                                            if ($status_pengerjaan == 'proses') { $badge_class = 'info'; $badge_text = 'Proses'; } 
                                            elseif ($status_pengerjaan == 'selesai') { $badge_class = 'success'; $badge_text = 'Selesai'; } 
                                            elseif ($status_pengerjaan == 'batal') { $badge_class = 'danger'; $badge_text = 'Batal'; }
                                        }
                                        ?>
                                        <span class="badge badge-<?php echo $badge_class; ?>"><?php echo $badge_text; ?></span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <?php if ($_SESSION['level'] == 'proyek' && $tugas['status_verifikasi'] == 'approved'): ?>
                                        <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#statusModal<?php echo $tugas['id']; ?>" title="Ubah Status Pengerjaan">
                                            <i class="fas fa-edit"></i> Ubah
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php } } else { ?>
                                <tr><td colspan="8" class="text-center">Belum ada tugas.</td></tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    mysqli_data_seek($sql_tugas, 0); // Kembali ke awal data untuk loop modal
    if (mysqli_num_rows($sql_tugas) > 0) {
        while ($tugas = mysqli_fetch_assoc($sql_tugas)) {
    ?>
    <div class="modal fade" id="statusModal<?php echo $tugas['id']; ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ubah Status Pengerjaan</h5>
                    <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
                </div>
                <form action="update_status_tugas.php" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="tugas_id" value="<?php echo $tugas['id']; ?>">
                        <p><strong>Tugas:</strong> <?php echo htmlspecialchars($tugas['nama_kegiatan']); ?></p>
                        <div class="form-group">
                            <label for="status">Status Pengerjaan</label>
                            <select name="status" class="form-control">
                                <option value="proses" <?php if($tugas['status'] == 'proses') echo 'selected'; ?>>Proses</option>
                                <option value="selesai" <?php if($tugas['status'] == 'selesai') echo 'selected'; ?>>Selesai</option>
                                <option value="batal" <?php if($tugas['status'] == 'batal') echo 'selected'; ?>>Batal</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php } } ?>

    <?php include 'includes/footer/footer.php'; ?>
</div>
</body>
</html>