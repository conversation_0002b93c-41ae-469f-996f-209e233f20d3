<?php
require_once '../includes/session_manager.php';
// Izinkan 'admin' dan 'proyek' untuk menjalankan skrip ini
check_session_auth(['admin', 'proyek']);
require_once '../koneksi.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Ambil data dari form
    $proyek_id = $_POST['proyek_id'];
    $nama_kegiatan = trim($_POST['nama_kegiatan']);
    $deskripsi = trim($_POST['deskripsi']);
    $tgl_mulai = $_POST['tgl_mulai'];
    $tgl_selesai = $_POST['tgl_selesai'];

    // Validasi dasar
    if (empty($proyek_id) || empty($nama_kegiatan) || empty($tgl_mulai) || empty($tgl_selesai)) {
        $_SESSION['error_message'] = "Semua kolom wajib diisi!";
        // Redirect kembali ke halaman asal
        header("Location: " . $_SERVER['HTTP_REFERER']);
        exit();
    }

    // Cek level user yang sedang login untuk menentukan status verifikasi
    $status_verifikasi = 'pending'; // Default untuk admin
    $verifikator_id = NULL;
    $tanggal_verifikasi = NULL;

    if ($_SESSION['level'] == 'proyek') {
        // Jika yang input adalah tim proyek, langsung set 'approved'
        $status_verifikasi = 'approved';
        $verifikator_id = $_SESSION['id_petugas']; // Catat dirinya sebagai verifikator
        $tanggal_verifikasi = date('Y-m-d H:i:s'); // Catat waktu verifikasi sekarang
    }

    // Siapkan perintah SQL INSERT yang aman
    $sql = "INSERT INTO tugas_proyek 
                (proyek_id, nama_kegiatan, deskripsi, tgl_mulai, tgl_selesai, status_verifikasi, verifikator_id, tanggal_verifikasi) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = mysqli_prepare($koneksi, $sql);
    
    // Bind parameter ke statement
    mysqli_stmt_bind_param($stmt, "isssssis", $proyek_id, $nama_kegiatan, $deskripsi, $tgl_mulai, $tgl_selesai, $status_verifikasi, $verifikator_id, $tanggal_verifikasi);

    // Eksekusi dan siapkan notifikasi
    if (mysqli_stmt_execute($stmt)) {
        if ($status_verifikasi == 'approved') {
            $_SESSION['success_message'] = "Tugas baru berhasil ditambahkan dan langsung disetujui!";
        } else {
            $_SESSION['success_message'] = "Tugas baru berhasil ditambahkan dan menunggu verifikasi.";
        }
    } else {
        $_SESSION['error_message'] = "Gagal menyimpan tugas.";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($koneksi);

    // Arahkan kembali ke halaman yang sesuai berdasarkan level user
    if ($_SESSION['level'] == 'admin') {
        // Jika yang menyimpan adalah admin, kembali ke form input admin
        header("Location: ../admin/input_tugas.php");
    } else {
        // Jika yang menyimpan adalah proyek, kembali ke form input proyek
        header("Location: input_tugas.php"); 
    }
    exit();

} else {
    // Jika file diakses langsung, arahkan ke dashboard proyek (sebagai default)
    header("Location: proyek.php");
    exit();
}
?>