<?php
require 'koneksi.php'; // Pastikan path ini benar

echo "<h3><PERSON><PERSON><PERSON> Proses Update Password...</h3>";

// 1. Ambil semua petugas yang password-nya masih plaintext (pan<PERSON><PERSON><PERSON> pendek)
$sql = "SELECT id_petugas, username, password FROM petugas WHERE LENGTH(password) < 60";
$result = mysqli_query($koneksi, $sql);

if (mysqli_num_rows($result) > 0) {
    // 2. Loop setiap petugas
    while ($row = mysqli_fetch_assoc($result)) {
        $id = $row['id_petugas'];
        $username = $row['username'];
        $plain_password = $row['password'];

        // 3. Buat hash dari password plaintext
        $hashed_password = password_hash($plain_password, PASSWORD_DEFAULT);

        // 4. Update baris di database dengan password yang sudah di-hash
        // Menggunakan prepared statement untuk keamanan
        $update_stmt = mysqli_prepare($koneksi, "UPDATE petugas SET password = ? WHERE id_petugas = ?");
        mysqli_stmt_bind_param($update_stmt, "si", $hashed_password, $id);

        if (mysqli_stmt_execute($update_stmt)) {
            echo "✅ Password untuk user '<strong>" . htmlspecialchars($username) . "</strong>' berhasil di-update.<br>";
        } else {
            echo "❌ Gagal meng-update password untuk user '" . htmlspecialchars($username) . "'.<br>";
        }
        mysqli_stmt_close($update_stmt);
    }
} else {
    echo "✅ Tidak ada password plaintext yang perlu di-update. Semua sudah aman!";
}

echo "<h3>Proses Selesai.</h3>";
mysqli_close($koneksi);
?>